import os
import cv2
import time
import rclpy
import logging
import threading
from rclpy.node import Node
from cv_bridge import CvBridge
from sensor_msgs.msg import Image
from apscheduler.schedulers.background import BackgroundScheduler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()  # 输出到控制台
    ]
)
logger = logging.getLogger(__name__)

# 图片保存间隔
SAVE_EVERY = 2
# 图片保存文件夹 映射至/data/ai_police/saved_images
SAVE_DIR = '/ai_police/saved_images'
# 订阅topic
TOPICS = [
    '/device_0_decode',
    '/device_1_decode',
    '/device_2_decode',
    '/device_3_decode'
]
# 图片清理间隔
CLEAR_EVERY = 4

def clear_images():
    now = int(time.time() * 1000)  # 当前UTC时间戳，单位毫秒
    for filename in os.listdir(SAVE_DIR):
        try:
            # 文件名格式: camr_device_2_decode_1751359356496.jpg
            timestamp_str = filename.split('_')[-1].replace('.jpg', '')
            file_time = int(timestamp_str)
            # 超过一定时间就删除
            if now - file_time > CLEAR_EVERY * 60 * 1000:
                file_path = os.path.join(SAVE_DIR, filename)
                os.remove(file_path)
            logger.info(f"已删除图片{filename}")
        except Exception as e:
            logger.error(f"删除图片{filename}时出错: {e}")

class ImageSaverNode(Node):

    def __init__(self):
        try:
            # 初始化ROS 2节点
            super().__init__('image_saver')
            logger.info('初始化 image saver node...')

            # cv_bridge可将ros image转为常用图片格式
            self.bridge = CvBridge()
            os.makedirs(SAVE_DIR, exist_ok=True)
            # 使用锁和计数机制，实现间隔一定频率才保存图片
            self.topic_counters = {topic: 0 for topic in TOPICS}  # 每个topic一个计数器
            self.counter_locks = {topic: threading.Lock() for topic in TOPICS}  # 每个topic一把锁
            # 遍历话题列表，为每个话题创建订阅者
            for i, topic_name in enumerate(TOPICS):
                self.create_subscription(
                    Image,
                    topic_name,
                    lambda msg, t=topic_name: self.image_callback(msg, t),  # 传递话题名称到回调函数
                    10  # QoS：消息队列深度
                )
                logger.info(f'订阅至: {topic_name}')
        except Exception as e:
            logger.error(f"节点初始化失败: {e}")

    def image_callback(self, img: Image, topic_name: str):
        # 使用锁和计数机制，实现间隔一定频率才保存图片
        save_image = False
        with self.counter_locks[topic_name]:
            self.topic_counters[topic_name] += 1
            if self.topic_counters[topic_name] % SAVE_EVERY == 0:
                save_image = True
        if not save_image:
            return
        try:
            sec = str(img.header.stamp.sec)
            nanosec = str(img.header.stamp.nanosec)[:3]
            nanosec = nanosec + '0' * (3 - len(nanosec))
            time = sec + nanosec
            # 将ROS Image消息转为OpenCV图像
            cv_image = self.bridge.imgmsg_to_cv2(img, desired_encoding='bgr8')
            # 生成文件名
            filename = os.path.join(
                SAVE_DIR,
                f"{topic_name.strip('/').replace('/', '_')}_{time}.jpg"
            )
            # 保存为JPEG
            cv2.imwrite(filename, cv_image)
            logger.info(f"已保存图片: {filename}")
        except Exception as e:
            logger.error(f"保存图片失败: {e}")

def main(args=None):
    rclpy.init(args=args)
    image_saver_node = ImageSaverNode()
    try:
        rclpy.spin(image_saver_node)
    except KeyboardInterrupt:
        logger.info('用户打断')
    finally:
        image_saver_node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    scheduler = BackgroundScheduler()
    scheduler.add_job(clear_images, 'cron', minute=f'*/{CLEAR_EVERY}')
    scheduler.start()
    main()
